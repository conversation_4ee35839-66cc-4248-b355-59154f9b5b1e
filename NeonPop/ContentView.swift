//
//  ContentView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import PhotosUI
import Photos

struct ContentView: View {
    @StateObject var processor = MultiLayerProcessor()
    @StateObject var undoManager = UndoRedoManager()
    @State var selectedPhoto: PhotosPickerItem?
    @State var showingImagePicker = false
    @State var showingSaveAlert = false
    @State var showingShareSheet = false
    @State var showingHelp = false
    @State var showingTextEditor = false
    @State var editingTextLayer: LayerModel?
    @State var canvasSize = CGSize(width: 375, height: 500) // 保存时的实际尺寸
    @State var displayCanvasSize = CGSize(width: 375, height: 500) // 屏幕显示尺寸
    @State var showingAspectRatioMenu = false
    @State var selectedAspectRatio: AspectRatio = .ratio3x4
    @State var isResizingCanvas = false // 跟踪画布是否正在调整尺寸

    // 新增：用于控制是否显示返回按钮
    @Environment(\.presentationMode) var presentationMode
    @State var showBackButton = true

    // 简化初始化 - 不再依赖项目管理系统
    init(showBackButton: Bool = true) {
        self._showBackButton = State(initialValue: showBackButton)

        print("🔧 ContentView初始化 - 简化模式")
    }

    // 初始化画布设置
    private func initializeCanvas() {
        print("🎨 初始化画布设置")

        // 设置默认画布尺寸
        canvasSize = CGSize(width: 375, height: 500)
        displayCanvasSize = CGSize(width: 375, height: 500)
        selectedAspectRatio = .ratio3x4

        // 清空图层
        processor.layers = []

        // 更新画布尺寸
        updateCanvasSize()

        // 更新合成图像
        Task {
            await processor.updateCompositeImage()
        }

        print("✅ 画布初始化完成")
    }



    var body: some View {
        GeometryReader { geometry in
            // 背景渐变
            LinearGradient(
                colors: [Color.black, CyberPunkStyle.electricBlue.opacity(0.3)],
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
            .overlay(
                VStack(spacing: 0) {
                    // 顶部工具栏 - 撤销/重做/保存
                    topToolbar
                        .padding(.top, geometry.safeAreaInsets.top + 5)
                        .padding(.horizontal)



                    // 白板编辑区域 - 真正居中显示
                    canvasArea
                        .padding(.top, 20)
                        .padding(.horizontal, 20)
                        .onChange(of: processor.layers.count) { _ in
                            // 当图层数量变化时重新计算画布尺寸
                            updateCanvasSize()
                        }

                    // 错误信息和抠图状态提示
                    if let errorMessage = processor.errorMessage {
                        HStack(spacing: 8) {
                            Image(systemName: processor.lastCutoutFailed ? "exclamationmark.triangle.fill" : "info.circle.fill")
                                .foregroundColor(processor.lastCutoutFailed ? .orange : .red)

                            Text(errorMessage)
                                .font(.system(size: 12))
                                .foregroundColor(.white)

                            if processor.lastCutoutFailed {
                                Button("重试") {
                                    if let failedLayer = processor.lastFailedLayer {
                                        Task {
                                            await processor.retryBackgroundRemoval(for: failedLayer)
                                        }
                                    }
                                }
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(CyberPunkStyle.neonPink)
                            }

                            Button("关闭") {
                                processor.errorMessage = nil
                                processor.lastCutoutFailed = false
                                processor.lastFailedLayer = nil
                            }
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white.opacity(0.7))
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.black.opacity(0.8))
                        .cornerRadius(8)
                        .padding(.horizontal)
                        .padding(.top, 5)
                    }

                    // 底部功能按钮 - 固定在底部
                    Spacer()

                    bottomButtons
                        .padding(.horizontal)
                        .padding(.bottom, geometry.safeAreaInsets.bottom + 10)
                }
            )
            .overlay(
                // 图层控制按钮 - 悬浮在画布下方
                VStack {
                    Spacer()

                    if !processor.layers.isEmpty && processor.layers.contains(where: { $0.isSelected }) {
                        layerOrderButtons
                            .padding(.horizontal)
                            .padding(.bottom, geometry.safeAreaInsets.bottom + 100) // 在底部按钮上方悬浮
                    }
                }
            )
        }
        .ignoresSafeArea(.keyboard) // 全局忽略键盘安全区域
        .photosPicker(isPresented: $showingImagePicker, selection: $selectedPhoto, matching: .images)
        .onChange(of: selectedPhoto) { newValue in
            Task {
                await loadSelectedPhoto()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .layerTransformUpdated)) { notification in
            // 图层变换时更新合成图像
            Task {
                await processor.updateCompositeImage()
            }
        }
        .alert("保存成功", isPresented: $showingSaveAlert) {
            Button("确定") { }
        } message: {
            Text("图像已保存到相册")
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(activityItems: [generateCanvasImage()])
        }
        .sheet(isPresented: $showingHelp) {
            HelpView()
        }
        .sheet(isPresented: $showingTextEditor) {
            if let editingLayer = editingTextLayer {
                TextEditingView(layer: editingLayer, processor: processor, undoManager: undoManager)
            }
        }

    }

    // 白板编辑区域
    private var canvasArea: some View {
        GeometryReader { geometry in
            // 计算画布在可用空间中的居中位置
            let canvasX = (geometry.size.width - displayCanvasSize.width) / 2
            let canvasY = (geometry.size.height - displayCanvasSize.height) / 2

            ZStack {
                // 蓝色背景白板 - 使用显示尺寸，改为直角矩形确保与保存一致
                Rectangle()
                    .fill(CyberPunkStyle.electricBlue)
                    .frame(width: displayCanvasSize.width, height: displayCanvasSize.height)
                    .shadow(color: CyberPunkStyle.electricBlue.opacity(0.6), radius: 20, x: 0, y: 8)
                    .shadow(color: Color.black.opacity(0.4), radius: 12, x: 0, y: 6)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                        .overlay(
                            // 图层容器 - 移除边界限制，允许图层超出背景
                            ZStack {
                                // 背景点击区域 - 用于取消选择
                                Color.clear
                                    .contentShape(Rectangle())
                                    .onTapGesture {
                                        // 点击空白区域取消选择并隐藏悬浮菜单
                                        processor.deselectAllLayers()
                                        processor.hideFloatingMenu()
                                        // 不再强制结束编辑模式，只能通过取消/确定按钮退出
                                    }



                                // 图层显示 - 移除边界限制
                                ForEach(processor.layers) { layer in
                                    LayerEditingView(
                                        layer: layer,
                                        processor: processor,
                                        undoManager: undoManager,
                                        canvasSize: displayCanvasSize
                                    )
                                }

                                // 空状态显示 - 应用名称和介绍
                                if processor.layers.isEmpty && !processor.isProcessing {
                                    ZStack {
                                        // 背景装饰文字 - 更丰富的布局
                                        Group {
                                            // 左上角区域
                                            VStack(spacing: 8) {
                                                Text("CYBERPUNK")
                                                    .font(.system(size: 22, weight: .black))
                                                    .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                    .blur(radius: 3)
                                                    .opacity(0.5)

                                                Text("2077")
                                                    .font(.system(size: 18, weight: .bold))
                                                    .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                    .blur(radius: 3)
                                                    .opacity(0.4)
                                            }
                                            .offset(x: -60, y: -40)

                                            // 右上角
                                            Text("POSTER")
                                                .font(.system(size: 16, weight: .semibold))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.4)
                                                .rotationEffect(.degrees(15))
                                                .offset(x: 50, y: -30)

                                            // 左下角
                                            Text("蒸汽波")
                                                .font(.system(size: 14, weight: .medium))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.3)
                                                .offset(x: -50, y: 35)

                                            // 右下角
                                            Text("VAPORWAVE")
                                                .font(.system(size: 12, weight: .light))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.3)
                                                .rotationEffect(.degrees(-10))
                                                .offset(x: 40, y: 40)

                                            // 中间偏右
                                            Text("フィルター")
                                                .font(.system(size: 14, weight: .regular))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.35)
                                                .rotationEffect(.degrees(90))
                                                .offset(x: 70, y: 0)

                                            // 更多装饰文字
                                            Text("NEON")
                                                .font(.system(size: 20, weight: .heavy))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.25)
                                                .rotationEffect(.degrees(-45))
                                                .offset(x: -80, y: 10)

                                            Text("RETRO")
                                                .font(.system(size: 16, weight: .bold))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.3)
                                                .offset(x: 0, y: -50)

                                            Text("未来")
                                                .font(.system(size: 18, weight: .black))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.35)
                                                .offset(x: 30, y: 10)

                                            Text("SYNTH")
                                                .font(.system(size: 14, weight: .medium))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.25)
                                                .rotationEffect(.degrees(30))
                                                .offset(x: -30, y: -20)

                                            Text("ネオン")
                                                .font(.system(size: 12, weight: .regular))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.3)
                                                .rotationEffect(.degrees(-15))
                                                .offset(x: 60, y: 20)

                                            Text("DIGITAL")
                                                .font(.system(size: 10, weight: .light))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.2)
                                                .rotationEffect(.degrees(75))
                                                .offset(x: -20, y: 50)

                                            Text("MATRIX")
                                                .font(.system(size: 15, weight: .semibold))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.3)
                                                .rotationEffect(.degrees(-60))
                                                .offset(x: 45, y: -10)

                                            Text("赛博")
                                                .font(.system(size: 16, weight: .bold))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.25)
                                                .offset(x: -10, y: 30)

                                            Text("WAVE")
                                                .font(.system(size: 13, weight: .medium))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.2)
                                                .rotationEffect(.degrees(45))
                                                .offset(x: -70, y: -10)

                                            Text("3D")
                                                .font(.system(size: 24, weight: .black))
                                                .foregroundColor(CyberPunkStyle.decorativeBlue)
                                                .blur(radius: 3)
                                                .opacity(0.15)
                                                .offset(x: 20, y: -35)

                                            // 圆形装饰
                                            Circle()
                                                .stroke(CyberPunkStyle.decorativeBlue.opacity(0.2), lineWidth: 1)
                                                .frame(width: 80, height: 80)
                                                .blur(radius: 2)
                                                .offset(x: -40, y: 0)

                                            // 小圆形装饰
                                            Circle()
                                                .stroke(CyberPunkStyle.decorativeBlue.opacity(0.15), lineWidth: 0.5)
                                                .frame(width: 40, height: 40)
                                                .blur(radius: 1)
                                                .offset(x: 55, y: 5)

                                            // 矩形装饰
                                            Rectangle()
                                                .stroke(CyberPunkStyle.decorativeBlue.opacity(0.1), lineWidth: 0.5)
                                                .frame(width: 30, height: 15)
                                                .blur(radius: 1)
                                                .rotationEffect(.degrees(25))
                                                .offset(x: -15, y: -35)
                                        }

                                        // 主标题和副标题
                                        VStack(spacing: 6) {
                                            Text("NEON POP")
                                                .font(.system(size: 28, weight: .heavy))
                                                .foregroundColor(CyberPunkStyle.neonPink)

                                            Text("3D 裸眼海报")
                                                .font(.system(size: 16, weight: .medium))
                                                .foregroundColor(CyberPunkStyle.neonPink.opacity(0.8))
                                        }
                                    }
                                }

                                // 画布调整指示器
                                if isResizingCanvas {
                                    VStack {
                                        Image(systemName: "aspectratio")
                                            .font(.system(size: 24))
                                            .foregroundColor(CyberPunkStyle.neonPink)
                                            .scaleEffect(1.2)

                                        Text("调整画布比例...")
                                            .font(.system(size: 12, weight: .medium))
                                            .foregroundColor(.white.opacity(0.8))
                                            .padding(.top, 4)
                                    }
                                    .padding()
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.black.opacity(0.6))
                                            .blur(radius: 1)
                                    )
                                    .transition(.opacity.combined(with: .scale))
                                }
                            }
                            // 移除 .clipped() 以允许图层超出背景边界
                        )
            }
            .onAppear {
                // 初始化画布
                initializeCanvas()
            }
            .onChange(of: selectedAspectRatio) { _ in
                // 标记开始调整尺寸
                isResizingCanvas = true

                // 使用动画让画布尺寸变化更平滑
                withAnimation(.easeInOut(duration: 0.3)) {
                    updateCanvasSize()
                }

                // 延迟重置状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    isResizingCanvas = false
                }
            }
            .overlay(
                // 全局悬浮菜单 - 相对于画布中心定位
                Group {
                    if processor.showingFloatingMenu,
                       let layer = processor.floatingMenuLayer {
                        // 将图层坐标转换为画布相对坐标
                        let canvasCenter = CGPoint(x: displayCanvasSize.width / 2, y: displayCanvasSize.height / 2)
                        let menuOffset = CGPoint(
                            x: processor.floatingMenuPosition.x,
                            y: processor.floatingMenuPosition.y
                        )

                        FloatingTextMenu(
                            layer: layer,
                            processor: processor,
                            undoManager: undoManager,
                            isVisible: .constant(true),
                            position: menuOffset
                        )
                        .zIndex(1000) // 确保在最顶层
                    }
                }
            )
        }
    }

    // 顶部工具栏 - 撤销/重做/保存
    private var topToolbar: some View {
        HStack {
            // 返回按钮（如果需要显示）
            if showBackButton {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 22, weight: .semibold))
                        .foregroundColor(CyberPunkStyle.neonPink)
                }
            } else {
                // 帮助按钮
                Button(action: {
                    showingHelp = true
                }) {
                    Image(systemName: "questionmark.circle")
                        .font(.system(size: 22))
                        .foregroundColor(CyberPunkStyle.neonPink)
                }
            }

            // 比例选择按钮
            Button(action: {
                showingAspectRatioMenu = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "aspectratio")
                        .font(.system(size: 16))
                    Text(selectedAspectRatio.rawValue)
                        .font(.system(size: 12, weight: .medium))
                }
                .foregroundColor(CyberPunkStyle.neonPink)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
            }
            .actionSheet(isPresented: $showingAspectRatioMenu) {
                ActionSheet(
                    title: Text("选择画布比例"),
                    buttons: AspectRatio.allCases.map { ratio in
                        .default(Text(ratio == selectedAspectRatio ? "✓ \(ratio.displayName)" : ratio.displayName)) {
                            if ratio != selectedAspectRatio {
                                selectedAspectRatio = ratio
                            }
                        }
                    } + [.cancel()]
                )
            }

            Spacer()



            // 撤销/重做/保存按钮组
            if !processor.layers.isEmpty {
                HStack(spacing: 12) {
                    // 撤销按钮
                    Button(action: {
                        undoManager.undo(layers: &processor.layers)
                        Task {
                            await processor.updateCompositeImage()
                        }
                    }) {
                        Image(systemName: "arrow.uturn.backward")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(
                                Circle()
                                    .fill(undoManager.canUndo ? CyberPunkStyle.decorativeBlue : Color.gray.opacity(0.5))
                                    .shadow(color: undoManager.canUndo ? CyberPunkStyle.decorativeBlue.opacity(0.5) : Color.clear, radius: 6)
                            )
                    }
                    .disabled(!undoManager.canUndo)

                    // 重做按钮
                    Button(action: {
                        undoManager.redo(layers: &processor.layers)
                        Task {
                            await processor.updateCompositeImage()
                        }
                    }) {
                        Image(systemName: "arrow.uturn.forward")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(
                                Circle()
                                    .fill(undoManager.canRedo ? CyberPunkStyle.decorativeBlue : Color.gray.opacity(0.5))
                                    .shadow(color: undoManager.canRedo ? CyberPunkStyle.decorativeBlue.opacity(0.5) : Color.clear, radius: 6)
                            )
                    }
                    .disabled(!undoManager.canRedo)

                    // 保存按钮
                    Button(action: {
                        saveCompositeImage()
                    }) {
                        Image(systemName: "square.and.arrow.down")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(
                                Circle()
                                    .fill(CyberPunkStyle.neonPink)
                                    .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 6)
                            )
                    }
                }
            }
        }
    }

    // 底部功能按钮
    private var bottomButtons: some View {
        let isEmpty = processor.layers.isEmpty
        let buttonSize: CGFloat = isEmpty ? 70 : 60
        let iconSize: CGFloat = isEmpty ? 28 : 24
        let glowRadius: CGFloat = isEmpty ? 12 : 8

        return HStack(spacing: isEmpty ? 30 : 20) {
            // 添加照片按钮
            Button(action: {
                showingImagePicker = true
            }) {
                VStack(spacing: 8) {
                    Image(systemName: "photo.badge.plus")
                        .font(.system(size: iconSize, weight: isEmpty ? .semibold : .regular))
                        .foregroundColor(.white)
                        .frame(width: buttonSize, height: buttonSize)
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(CyberPunkStyle.electricBlue)
                                .shadow(color: CyberPunkStyle.electricBlue.opacity(0.5), radius: glowRadius)
                        )
                        .scaleEffect(isEmpty ? 1.05 : 1.0)

                    Text("添加照片")
                        .font(.system(size: isEmpty ? 14 : 12, weight: isEmpty ? .semibold : .medium))
                        .foregroundColor(.white.opacity(isEmpty ? 1.0 : 0.8))
                }
            }

            // 添加背景文字按钮
            Button(action: {
                addDecorativeTextLayer()
            }) {
                VStack(spacing: 8) {
                    Image(systemName: "textformat.size")
                        .font(.system(size: iconSize, weight: isEmpty ? .semibold : .regular))
                        .foregroundColor(.white)
                        .frame(width: buttonSize, height: buttonSize)
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(CyberPunkStyle.decorativeBlue)
                                .shadow(color: CyberPunkStyle.decorativeBlue.opacity(0.5), radius: glowRadius)
                        )
                        .scaleEffect(isEmpty ? 1.05 : 1.0)

                    Text("背景文字")
                        .font(.system(size: isEmpty ? 14 : 12, weight: isEmpty ? .semibold : .medium))
                        .foregroundColor(.white.opacity(isEmpty ? 1.0 : 0.8))
                }
            }

            // 添加标题文字按钮
            Button(action: {
                addTitleTextLayer()
            }) {
                VStack(spacing: 8) {
                    Image(systemName: "textformat")
                        .font(.system(size: iconSize, weight: isEmpty ? .semibold : .regular))
                        .foregroundColor(.white)
                        .frame(width: buttonSize, height: buttonSize)
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(CyberPunkStyle.neonPink)
                                .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: glowRadius)
                        )
                        .scaleEffect(isEmpty ? 1.05 : 1.0)

                    Text("标题文字")
                        .font(.system(size: isEmpty ? 14 : 12, weight: isEmpty ? .semibold : .medium))
                        .foregroundColor(.white.opacity(isEmpty ? 1.0 : 0.8))
                }
            }
        }
        .animation(.easeInOut(duration: 0.3), value: isEmpty)
    }

    // 图层顺序调整按钮
    private var layerOrderButtons: some View {
        HStack(spacing: 20) {
            Spacer()

            // 下移图层按钮
            Button(action: {
                if let selectedLayer = processor.layers.first(where: { $0.isSelected }) {
                    let oldIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) ?? 0
                    processor.moveLayerDown(selectedLayer)
                    let newIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) ?? 0
                    undoManager.recordAction(.moveLayer(selectedLayer, oldIndex, newIndex))
                }
            }) {
                VStack(spacing: 4) {
                    Image(systemName: "arrow.down")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(CyberPunkStyle.decorativeBlue.opacity(0.8))
                                .shadow(color: CyberPunkStyle.decorativeBlue.opacity(0.5), radius: 6)
                        )

                    Text("下移")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            .disabled(!canMoveSelectedLayerDown())
            .opacity(canMoveSelectedLayerDown() ? 1.0 : 0.5)

            // 上移图层按钮
            Button(action: {
                if let selectedLayer = processor.layers.first(where: { $0.isSelected }) {
                    let oldIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) ?? 0
                    processor.moveLayerUp(selectedLayer)
                    let newIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) ?? 0
                    undoManager.recordAction(.moveLayer(selectedLayer, oldIndex, newIndex))
                }
            }) {
                VStack(spacing: 4) {
                    Image(systemName: "arrow.up")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(CyberPunkStyle.decorativeBlue.opacity(0.8))
                                .shadow(color: CyberPunkStyle.decorativeBlue.opacity(0.5), radius: 6)
                        )

                    Text("上移")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            .disabled(!canMoveSelectedLayerUp())
            .opacity(canMoveSelectedLayerUp() ? 1.0 : 0.5)

            // 颜色变换按钮
            Button(action: {
                if let selectedLayer = processor.layers.first(where: { $0.isSelected }) {
                    let oldValue = selectedLayer.isColorTransformed
                    processor.toggleLayerColorTransform(selectedLayer)
                    undoManager.recordAction(.toggleLayerColorTransform(selectedLayer, oldValue))
                }
            }) {
                VStack(spacing: 4) {
                    Image(systemName: getSelectedLayer()?.isColorTransformed == true ? "paintpalette.fill" : "paintpalette")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill((getSelectedLayer()?.isColorTransformed == true ? CyberPunkStyle.neonPink : CyberPunkStyle.decorativeBlue).opacity(0.8))
                                .shadow(color: (getSelectedLayer()?.isColorTransformed == true ? CyberPunkStyle.neonPink : CyberPunkStyle.decorativeBlue).opacity(0.5), radius: 6)
                        )

                    Text("变色")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }

            // 删除按钮
            Button(action: {
                if let selectedLayer = processor.layers.first(where: { $0.isSelected }) {
                    let originalIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) ?? 0
                    undoManager.recordAction(.removeLayer(selectedLayer, originalIndex))
                    processor.removeLayer(selectedLayer)
                }
            }) {
                VStack(spacing: 4) {
                    Image(systemName: "trash")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(Color.red.opacity(0.8))
                                .shadow(color: Color.red.opacity(0.5), radius: 6)
                        )

                    Text("删除")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }

            // 手动抠图按钮（只在选中图片图层且抠图失败时显示）
            if let selectedLayer = getSelectedLayer(),
               selectedLayer.type == .image,
               processor.lastCutoutFailed && processor.lastFailedLayer?.id == selectedLayer.id {
                Button(action: {
                    Task {
                        await processor.retryBackgroundRemoval(for: selectedLayer)
                    }
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: "scissors")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(
                                Circle()
                                    .fill(Color.orange.opacity(0.8))
                                    .shadow(color: Color.orange.opacity(0.5), radius: 6)
                            )

                        Text("抠图")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }

            Spacer()
        }
    }

    // 控制按钮 (保留原有的，以防需要)
    private var controlButtons: some View {
        HStack(spacing: 15) {
            // 添加照片按钮
            Button(action: {
                showingImagePicker = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "photo.badge.plus")
                        .font(.system(size: 14))
                    Text("添加照片")
                        .font(.system(size: 14, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(CyberPunkStyle.electricBlue)
                        .shadow(color: CyberPunkStyle.electricBlue.opacity(0.5), radius: 8)
                )
            }

            // 添加文字按钮
            Button(action: {
                addTextLayer()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "textformat")
                        .font(.system(size: 14))
                    Text("添加文字")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(CyberPunkStyle.neonPink)
                        .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 8)
                )
            }

            // 撤销按钮
            Button(action: {
                undoManager.undo(layers: &processor.layers)
                Task {
                    await processor.updateCompositeImage()
                }
            }) {
                Image(systemName: "arrow.uturn.backward")
                    .font(.system(size: 14))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(undoManager.canUndo ? CyberPunkStyle.decorativeBlue : Color.gray.opacity(0.5))
                            .shadow(color: undoManager.canUndo ? CyberPunkStyle.decorativeBlue.opacity(0.5) : Color.clear, radius: 8)
                    )
            }
            .disabled(!undoManager.canUndo)

            // 重做按钮
            Button(action: {
                undoManager.redo(layers: &processor.layers)
                Task {
                    await processor.updateCompositeImage()
                }
            }) {
                Image(systemName: "arrow.uturn.forward")
                    .font(.system(size: 14))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(undoManager.canRedo ? CyberPunkStyle.decorativeBlue : Color.gray.opacity(0.5))
                            .shadow(color: undoManager.canRedo ? CyberPunkStyle.decorativeBlue.opacity(0.5) : Color.clear, radius: 8)
                    )
            }
            .disabled(!undoManager.canRedo)

            // 保存和分享按钮
            if !processor.layers.isEmpty {
                HStack(spacing: 12) {
                    // 保存按钮
                    Button(action: {
                        saveCompositeImage()
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: "square.and.arrow.down")
                                .font(.system(size: 14))
                            Text("保存")
                                .font(.system(size: 14, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(CyberPunkStyle.neonPink)
                                .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 8)
                        )
                    }

                    // 分享按钮
                    Button(action: {
                        showingShareSheet = true
                    }) {
                        Image(systemName: "square.and.arrow.up")
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(Color.gray.opacity(0.8))
                                    .shadow(color: Color.gray.opacity(0.5), radius: 8)
                            )
                    }
                }
            }
        }
    }
}

#Preview {
    ContentView(showBackButton: false)
}
